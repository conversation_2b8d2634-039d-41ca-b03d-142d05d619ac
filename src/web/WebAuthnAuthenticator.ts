import * as http from "node:http";
import * as webauthn from "@simplewebauthn/server";
import type {
  AuthenticationResponseJSON,
  RegistrationResponseJSON,
} from "@simplewebauthn/typescript-types";
import Cookies from "cookies";

import Authenticator, {
  Account,
  AccountRepository,
  registerWebAuthnCredential,
  SignInConfirmationCode,
  WebAuthnCredential,
} from "../app/Authenticator.js";
import { getBody } from "./_server.types.js";

export default class WebAuthnAuthenticator implements Authenticator {
  constructor(
    private accountRepository: AccountRepository,
    private cookieSigningKey: string,
    private req: http.IncomingMessage,
    private res: http.ServerResponse,
    private expectedOrigin: string[],
    private expectedRPID: string[],
  ) {}

  async getCurrentAccount(): Promise<Account | null> {
    const cookies = new Cookies(this.req, this.res, {
      keys: [this.cookieSigningKey],
    });
    const credentialId = cookies.get("zyr_webauthn_credential_id", {
      signed: true,
    });

    if (!credentialId) {
      return null;
    }

    const [account] =
      await this.accountRepository.findByWebauthnCredentialId(credentialId);

    return account;
  }

  async handleRegistrationOptionsRequest(
    req: http.IncomingMessage,
    res: http.ServerResponse,
  ): Promise<void> {
    const [account] = await this.getAccountForRegistration(req);

    if (!account || !req.headers.origin) {
      badRequest(res);

      return;
    }

    const origin = new URL(req.headers.origin).hostname;

    const options = webauthn.generateRegistrationOptions({
      rpName: "Зырь!",
      rpID: origin,
      userID: account.id,
      userName: account.name,
      authenticatorSelection: {
        residentKey: "required",
        userVerification: "preferred",
      },
    });

    const cookies = new Cookies(req, res, {
      keys: [this.cookieSigningKey],
    });

    cookies.set("zyr_webauthn_challenge", options.challenge, {
      maxAge: 10 * 60 * 1000,
      signed: true,
    });

    res.write(JSON.stringify(options));
    res.end();
  }

  // eslint-disable-next-line @typescript-eslint/require-await
  async handleAuthenticationOptionsRequest(
    req: http.IncomingMessage,
    res: http.ServerResponse,
  ): Promise<void> {
    const options = webauthn.generateAuthenticationOptions({
      userVerification: "preferred",
    });

    const cookies = new Cookies(req, res, {
      keys: [this.cookieSigningKey],
    });

    cookies.set("zyr_webauthn_challenge", options.challenge, {
      maxAge: 10 * 60 * 1000,
      signed: true,
    });

    res.write(JSON.stringify(options));
    res.end();
  }

  async handleVerifyRegistrationRequest(
    req: http.IncomingMessage,
    res: http.ServerResponse,
  ): Promise<void> {
    // eslint-disable-next-line prefer-const
    let [account, confirmationCode] = await this.getAccountForRegistration(req);
    const cookies = new Cookies(req, res, {
      keys: [this.cookieSigningKey],
    });

    const expectedChallenge = cookies.get("zyr_webauthn_challenge");

    if (
      !account ||
      !confirmationCode ||
      req.headers["content-type"] !== "application/json" ||
      !expectedChallenge
    ) {
      badRequest(res);

      return;
    }

    const body = await getBody(req);
    const registration = JSON.parse(body) as RegistrationResponseJSON;

    const verification = await webauthn.verifyRegistrationResponse({
      response: registration,
      expectedChallenge,
      expectedOrigin: this.expectedOrigin,
      expectedRPID: this.expectedRPID,
      requireUserVerification: false,
    });

    if (!verification.registrationInfo) {
      badRequest(res);

      return;
    }

    const credential: WebAuthnCredential = {
      credentialId: Buffer.from(
        verification.registrationInfo.credentialID,
      ).toString("base64url"),
      credentialPublicKey: Buffer.from(
        verification.registrationInfo.credentialPublicKey,
      ).toString("base64url"),
      counter: verification.registrationInfo.counter,
      credentialDeviceType: verification.registrationInfo.credentialDeviceType,
      credentialBackedUp: verification.registrationInfo.credentialBackedUp,
      relyingPartyId: verification.registrationInfo.rpID,
    };

    account = registerWebAuthnCredential(account, credential, confirmationCode);

    await this.accountRepository.set(account);

    cookies.set("zyr_webauthn_credential_id", credential.credentialId, {
      maxAge: 2 * 24 * 60 * 60 * 1000,
      signed: true,
    });
    cookies.set("zyr_webauthn_challenge", null);

    // eslint-disable-next-line require-atomic-updates
    res.statusCode = 204;

    res.end();
  }

  async handleVerifyAuthenticationRequest(
    req: http.IncomingMessage,
    res: http.ServerResponse,
  ): Promise<void> {
    const cookies = new Cookies(req, res, {
      keys: [this.cookieSigningKey],
    });

    const expectedChallenge = cookies.get("zyr_webauthn_challenge");

    if (
      req.headers["content-type"] !== "application/json" ||
      !expectedChallenge
    ) {
      badRequest(res);

      return;
    }

    const body = await getBody(req);
    const authentication = JSON.parse(body) as AuthenticationResponseJSON;

    const [account, credential] =
      await this.accountRepository.findByWebauthnCredentialId(
        authentication.id,
      );

    if (!account || !credential) {
      badRequest(res);

      return;
    }

    const verification = await webauthn.verifyAuthenticationResponse({
      response: authentication,
      authenticator: {
        credentialPublicKey: Buffer.from(
          credential.credentialPublicKey,
          "base64url",
        ),
        credentialID: Buffer.from(credential.credentialId, "base64url"),
        counter: credential.counter,
        transports: credential.transports,
      },
      expectedChallenge,
      expectedOrigin: this.expectedOrigin,
      expectedRPID: this.expectedRPID,
      requireUserVerification: false,
    });

    if (!verification.verified) {
      badRequest(res);

      return;
    }

    cookies.set("zyr_webauthn_credential_id", credential.credentialId, {
      maxAge: 400 * 24 * 60 * 60 * 1000,
      signed: true,
    });
    cookies.set("zyr_webauthn_challenge", null);

    // eslint-disable-next-line require-atomic-updates
    res.statusCode = 303;
    res.setHeader("Location", `/${account.name}`);

    res.end();
  }

  private async getAccountForRegistration(
    req: http.IncomingMessage,
  ): Promise<[Account, SignInConfirmationCode] | [null, null]> {
    if (!req.headers.referer) {
      return [null, null];
    }

    const referer = new URL(req.headers.referer);
    const code = referer.searchParams.get("code");

    if (!code) {
      return [null, null];
    }

    const [account, confirmationCode] =
      await this.accountRepository.findBySignInConfirmationCode(code);

    if (!account || !confirmationCode || confirmationCode.used) {
      return [null, null];
    }

    return [account, confirmationCode];
  }
}

function badRequest(res: http.ServerResponse): void {
  res.statusCode = 400;
  res.write("Bad Request");
  res.end();
}
