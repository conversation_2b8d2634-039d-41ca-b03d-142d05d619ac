import assert from "node:assert";
import http from "node:http";
import { create<PERSON><PERSON>, <PERSON>lug<PERSON> } from "graphql-yoga";

import Mu<PERSON><PERSON>and<PERSON> from "../app/MutationHandler.js";
import ConnectionPool from "../postgres/ConnectionPool.js";
import PostgresAccountRepository from "../postgres/PostgresAccountRepository.js";
import PostgresGraphQlExecutableSchema from "../postgres/PostgresGraphQlExecutableSchema/index.js";
import PostgresGraphQlResolverContext from "../postgres/PostgresGraphQlResolverContext.js";
import PostgresInviteRepository from "../postgres/PostgresInviteRepository.js";
import PostgresMarkRepository from "../postgres/PostgresMarkRepository.js";
import PostgresWatchlistRepository from "../postgres/PostgresWatchlistRepository.js";
import WebAuthnAuthenticator from "./WebAuthnAuthenticator.js";

assert(
  process.env.POSTGRES_CONNECTION_STRING,
  "Missing POSTGRES_CONNECTION_STRING env variable",
);
assert(
  process.env.COOKIE_SIGNING_KEY,
  "Missing COOKIE_SIGNING_KEY env variable",
);

const cookieSigningKey = process.env.COOKIE_SIGNING_KEY;
const pool = new ConnectionPool(process.env.POSTGRES_CONNECTION_STRING);
const accountRepository = new PostgresAccountRepository(pool);
const markRepository = new PostgresMarkRepository(pool);
const watchistRepository = new PostgresWatchlistRepository(pool);
const demoAccountId = "789114";
const webAuthnOrigins = ["http://localhost:8000", "https://zyr.best"];
const webAuthnRPIDs = ["localhost", "zyr.best"];

export const inviteRepository = new PostgresInviteRepository(pool);

export function getWebAuthnHandler(
  req: http.IncomingMessage,
  res: http.ServerResponse,
): WebAuthnAuthenticator {
  return new WebAuthnAuthenticator(
    accountRepository,
    cookieSigningKey,
    req,
    res,
    webAuthnOrigins,
    webAuthnRPIDs,
  );
}

export interface GraphQlContext {
  req: http.IncomingMessage;
  res: http.ServerResponse;
  ssr: boolean;
}

const skipJsonSerializationForSsrPlugin: Plugin<
  Record<string, unknown>,
  GraphQlContext
> = {
  onResultProcess(payload) {
    if ((payload.serverContext as GraphQlContext).ssr) {
      payload.setResultProcessor((result): Response => {
        return {
          async json() {
            return result;
          },
        } as Response;
      }, "*.*");
    }
  },
};

export const yogaInstance = createYoga<
  GraphQlContext,
  PostgresGraphQlResolverContext
>({
  schema: PostgresGraphQlExecutableSchema,
  maskedErrors: false,
  context: async (ctx) => {
    const authenticator = getWebAuthnHandler(ctx.req, ctx.res);
    const account = await authenticator.getCurrentAccount();
    let accountOrDemoAccount = account;

    if (!accountOrDemoAccount) {
      accountOrDemoAccount = await accountRepository.find(demoAccountId);
      assert(accountOrDemoAccount, "At least demo account should exist");
      accountOrDemoAccount.name = "demo";
    }

    return {
      account,
      accountOrDemoAccount,
      pool,
      mutationHandler: getMutationHandler(ctx.req, ctx.res),
    };
  },
  plugins: [skipJsonSerializationForSsrPlugin],
});

function getMutationHandler(
  req: http.IncomingMessage,
  res: http.ServerResponse,
): MutationHandler {
  return new MutationHandler(
    getWebAuthnHandler(req, res),
    markRepository,
    watchistRepository,
  );
}
