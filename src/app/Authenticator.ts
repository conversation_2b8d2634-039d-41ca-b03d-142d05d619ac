import assert from "node:assert";

export default interface Authenticator {
  getCurrentAccount(): Promise<Account | null>;
}

export interface AccountRepository {
  find(id: string): Promise<Account | null>;
  findByWebauthnCredentialId(
    credentialId: string,
  ): Promise<[Account, WebAuthnCredential] | [null, null]>;
  findBySignInConfirmationCode(
    confirmationCode: string,
  ): Promise<[Account, SignInConfirmationCode] | [null, null]>;
  findByKinopoiskIds(kinopoiskIds: string[]): Promise<(Account | null)[]>;
  set(account: Account): Promise<void>;
}

export interface Account {
  id: string;
  name: string;
  kinopoiskId: string;
  signInConfirmationCodes: SignInConfirmationCode[];
  webAuthnCredentials: WebAuthnCredential[];
}

export interface WebAuthnCredential {
  credentialId: string;
  credentialPublicKey: string;
  counter: number;
  credentialDeviceType: string;
  credentialBackedUp: boolean;
  transports?: AuthenticatorTransport[];
  relyingPartyId?: string;
}

export type SignInConfirmationCode =
  | {
      code: string;
      credentialId: string;
      used: true;
    }
  | {
      code: string;
      credentialId: null;
      used: false;
    };

export function registerWebAuthnCredential(
  account: Account,
  credential: WebAuthnCredential,
  confirmationCode: SignInConfirmationCode,
): Account {
  const signInConfirmationCodes = [...account.signInConfirmationCodes];
  const codeIndex = signInConfirmationCodes.findIndex(
    (x) => x.code === confirmationCode.code,
  );

  assert(codeIndex !== -1, "Confirmation code not found");
  assert.equal(
    confirmationCode,
    signInConfirmationCodes[codeIndex],
    "Confirmation codes don't match",
  );

  const usedConfirmationCode: SignInConfirmationCode = {
    code: confirmationCode.code,
    credentialId: credential.credentialId,
    used: true,
  };

  signInConfirmationCodes.splice(codeIndex, 1, usedConfirmationCode);

  const newAccount = {
    ...account,
    signInConfirmationCodes,
    webAuthnCredentials: [...account.webAuthnCredentials, credential],
  };

  return newAccount;
}
